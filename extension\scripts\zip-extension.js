import { readdir, stat } from 'fs/promises';
import { join, relative } from 'path';
import { createWriteStream } from 'fs';
import { createGzip } from 'zlib';
import { create as createArchive } from 'archiver';

const DIST_DIR = join(process.cwd(), 'dist');
const OUTPUT_FILE = join(process.cwd(), 'nucnuc-chrome-extension.zip');

/**
 * Creates a zip archive of the extension
 */
async function zipExtension() {
  try {
    // Create output stream
    const output = createWriteStream(OUTPUT_FILE);
    const archive = createArchive('zip', {
      zlib: { level: 9 },
    });

    // Listen for archive events
    output.on('close', () => {
      const size = archive.pointer();
      console.log(`Extension packaged successfully! (${(size / 1024 / 1024).toFixed(2)} MB)`);
      console.log(`Archive created at: ${OUTPUT_FILE}`);
    });

    archive.on('error', (err) => {
      throw err;
    });

    // Pipe archive to file
    archive.pipe(output);

    // Add files and directories recursively
    await addFilesToArchive(archive, DIST_DIR);

    // Finalize the archive
    await archive.finalize();
  } catch (error) {
    console.error('Error creating extension archive:', error);
    process.exit(1);
  }
}

/**
 * Add files to archive recursively
 * @param {Object} archive The archive instance
 * @param {string} dir Directory to add
 */
async function addFilesToArchive(archive, dir) {
  const files = await readdir(dir);

  for (const file of files) {
    const filePath = join(dir, file);
    const stats = await stat(filePath);

    if (stats.isDirectory()) {
      await addFilesToArchive(archive, filePath);
    } else {
      const relativePath = relative(DIST_DIR, filePath);
      archive.file(filePath, { name: relativePath });
    }
  }
}

zipExtension();
