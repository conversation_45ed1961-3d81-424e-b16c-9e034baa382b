{"name": "nucnuc-chrome-extension", "version": "1.0.0", "description": "A Chrome extension for tab and bookmark management", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build && node scripts/build-extension.js", "preview": "vite preview", "check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "zip": "node scripts/zip-extension.js"}, "dependencies": {}, "devDependencies": {"@types/chrome": "^0.0.260", "@types/node": "^20.10.5", "archiver": "^6.0.1", "rimraf": "^5.0.1", "typescript": "^5.2.2", "vite": "^5.0.0"}}