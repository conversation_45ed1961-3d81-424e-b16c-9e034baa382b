/**
 * UI Entry Point
 * Handles all UI interactions and state management for the sidepanel
 */

import { Settings, StatusType } from '../shared/types';

/**
 * UI Controller for the extension
 */
class SidePanelController {
  private settings: Settings;
  private elements: {
    app: HTMLElement;
    themeToggleBtn: HTMLElement;
    groupTabsBtn: HTMLElement;
    ungroupTabsBtn: HTMLElement;
    removeDuplicatesBtn: HTMLElement;
    autoGroupTabs: HTMLInputElement;
    cleanupDuplicates: HTMLInputElement;
    minTabsPerGroup: HTMLInputElement;
    colorScheme: HTMLSelectElement;
    status: HTMLElement;
  };

  constructor() {
    // Initialize with default settings
    this.settings = {
      theme: 'light',
      notifications: true,
      autoGroupTabs: true,
      cleanupDuplicates: true,
      tabGrouping: {
        minTabsPerGroup: 2,
        colorScheme: 'auto'
      }
    };

    // Get DOM elements
    this.elements = {
      app: document.getElementById('app')!,
      themeToggleBtn: document.getElementById('theme-toggle-btn')!,
      groupTabsBtn: document.getElementById('group-tabs-btn')!,
      ungroupTabsBtn: document.getElementById('ungroup-tabs-btn')!,
      removeDuplicatesBtn: document.getElementById('remove-duplicates-btn')!,
      autoGroupTabs: document.getElementById('auto-group-tabs') as HTMLInputElement,
      cleanupDuplicates: document.getElementById('cleanup-duplicates') as HTMLInputElement,
      minTabsPerGroup: document.getElementById('min-tabs-per-group') as HTMLInputElement,
      colorScheme: document.getElementById('color-scheme') as HTMLSelectElement,
      status: document.getElementById('status')!
    };

    // Initialize UI
    this.init();
  }

  /**
   * Initialize the UI
   */
  async init() {
    try {
      // Load settings from storage
      await this.loadSettings();
      
      // Set up event listeners
      this.setupEventListeners();
      
      // Update UI with current settings
      this.updateUI();
      
      // Show ready status
      this.showStatus('Ready', 'info');
    } catch (error) {
      console.error('Error initializing UI:', error);
      this.showStatus('Error initializing UI', 'error');
    }
  }

  /**
   * Load settings from storage
   */
  private async loadSettings() {
    return new Promise<void>((resolve, reject) => {
      chrome.runtime.sendMessage({ type: 'GET_SETTINGS' }, (response) => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
          return;
        }

        if (response && response.success) {
          this.settings = response.data;
          resolve();
        } else {
          reject(response?.error || 'Failed to load settings');
        }
      });
    });
  }

  /**
   * Save settings to storage
   */
  private async saveSettings() {
    return new Promise<void>((resolve, reject) => {
      chrome.runtime.sendMessage(
        { type: 'UPDATE_SETTINGS', data: this.settings },
        (response) => {
          if (chrome.runtime.lastError) {
            reject(chrome.runtime.lastError);
            return;
          }

          if (response && response.success) {
            resolve();
          } else {
            reject(response?.error || 'Failed to save settings');
          }
        }
      );
    });
  }

  /**
   * Set up event listeners for UI elements
   */
  private setupEventListeners() {
    // Theme toggle
    this.elements.themeToggleBtn.addEventListener('click', this.handleThemeToggle.bind(this));

    // Tab management
    this.elements.groupTabsBtn.addEventListener('click', this.handleGroupTabs.bind(this));
    this.elements.ungroupTabsBtn.addEventListener('click', this.handleUngroupTabs.bind(this));
    this.elements.removeDuplicatesBtn.addEventListener('click', this.handleRemoveDuplicates.bind(this));

    // Settings changes
    this.elements.autoGroupTabs.addEventListener('change', this.handleSettingsChange.bind(this));
    this.elements.cleanupDuplicates.addEventListener('change', this.handleSettingsChange.bind(this));
    this.elements.minTabsPerGroup.addEventListener('change', this.handleSettingsChange.bind(this));
    this.elements.colorScheme.addEventListener('change', this.handleSettingsChange.bind(this));
  }

  /**
   * Update UI elements to reflect current settings
   */
  private updateUI() {
    // Set theme
    this.elements.app.className = `theme-${this.settings.theme}`;

    // Set toggle states
    this.elements.autoGroupTabs.checked = this.settings.autoGroupTabs;
    this.elements.cleanupDuplicates.checked = this.settings.cleanupDuplicates;
    
    // Set other settings
    this.elements.minTabsPerGroup.value = this.settings.tabGrouping.minTabsPerGroup.toString();
    this.elements.colorScheme.value = this.settings.tabGrouping.colorScheme;
  }

  /**
   * Handle theme toggle
   */
  private handleThemeToggle() {
    this.settings.theme = this.settings.theme === 'light' ? 'dark' : 'light';
    this.elements.app.className = `theme-${this.settings.theme}`;
    this.saveSettings().catch(error => {
      console.error('Error saving theme setting:', error);
      this.showStatus('Error saving theme setting', 'error');
    });
  }

  /**
   * Handle group tabs button click
   */
  private async handleGroupTabs() {
    try {
      this.showStatus('Grouping tabs...', 'info');
      
      const response = await new Promise<any>((resolve, reject) => {
        chrome.runtime.sendMessage({ type: 'ORGANIZE_TABS' }, (response) => {
          if (chrome.runtime.lastError) {
            reject(chrome.runtime.lastError);
            return;
          }
          resolve(response);
        });
      });

      if (response.success) {
        this.showStatus(`Grouped tabs into ${response.groupCount} groups`, 'success');
      } else {
        throw new Error(response.error || 'Failed to group tabs');
      }
    } catch (error) {
      console.error('Error grouping tabs:', error);
      this.showStatus('Error grouping tabs', 'error');
    }
  }

  /**
   * Handle ungroup tabs button click
   */
  private async handleUngroupTabs() {
    try {
      this.showStatus('Ungrouping tabs...', 'info');
      
      const response = await new Promise<any>((resolve, reject) => {
        chrome.runtime.sendMessage({ type: 'UNGROUP_TABS' }, (response) => {
          if (chrome.runtime.lastError) {
            reject(chrome.runtime.lastError);
            return;
          }
          resolve(response);
        });
      });

      if (response.success) {
        this.showStatus(`Ungrouped ${response.ungrouped} tabs`, 'success');
      } else {
        throw new Error(response.error || 'Failed to ungroup tabs');
      }
    } catch (error) {
      console.error('Error ungrouping tabs:', error);
      this.showStatus('Error ungrouping tabs', 'error');
    }
  }

  /**
   * Handle remove duplicates button click
   */
  private async handleRemoveDuplicates() {
    try {
      this.showStatus('Removing duplicate tabs...', 'info');
      
      const response = await new Promise<any>((resolve, reject) => {
        chrome.runtime.sendMessage({ type: 'REMOVE_DUPLICATES' }, (response) => {
          if (chrome.runtime.lastError) {
            reject(chrome.runtime.lastError);
            return;
          }
          resolve(response);
        });
      });

      if (response.success) {
        this.showStatus(`Removed ${response.removed} duplicate tabs`, 'success');
      } else {
        throw new Error(response.error || 'Failed to remove duplicate tabs');
      }
    } catch (error) {
      console.error('Error removing duplicate tabs:', error);
      this.showStatus('Error removing duplicate tabs', 'error');
    }
  }

  /**
   * Handle settings changes
   */
  private handleSettingsChange() {
    // Update settings from UI
    this.settings.autoGroupTabs = this.elements.autoGroupTabs.checked;
    this.settings.cleanupDuplicates = this.elements.cleanupDuplicates.checked;
    
    this.settings.tabGrouping.minTabsPerGroup = Number(this.elements.minTabsPerGroup.value);
    this.settings.tabGrouping.colorScheme = this.elements.colorScheme.value as any;

    // Save settings
    this.saveSettings()
      .then(() => {
        this.showStatus('Settings saved', 'success');
      })
      .catch(error => {
        console.error('Error saving settings:', error);
        this.showStatus('Error saving settings', 'error');
      });
  }

  /**
   * Show status message
   * @param message Message to show
   * @param type Status type
   * @param duration Duration in ms (0 for permanent)
   */
  private showStatus(message: string, type: StatusType, duration = 3000) {
    this.elements.status.textContent = message;
    this.elements.status.className = `status ${type}`;

    // Clear previous timeout
    if (this.statusTimeout) {
      clearTimeout(this.statusTimeout);
    }

    // Set new timeout if duration is non-zero
    if (duration > 0) {
      this.statusTimeout = setTimeout(() => {
        this.elements.status.textContent = '';
        this.elements.status.className = 'status';
      }, duration);
    }
  }

  private statusTimeout: number | null = null;
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new SidePanelController();
});
