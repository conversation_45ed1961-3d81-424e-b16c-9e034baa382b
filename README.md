# NucNuc Chrome Extension

A lean, efficient Chrome extension that optimizes tab and bookmark organizing automatically.

## Features

- **Tab Management**
  - Automatic tab grouping by domain
  - Manual tab grouping with custom options
  - One-click ungroup all tabs
  - Duplicate tab removal
  - Customizable tab group settings

- **User Experience**
  - Side panel interface for mouse-only interactions
  - Light/Dark theme support
  - Status notifications
  - Minimal, clean interface

## Project Structure

```
nucnuc_new/
├── extension/                   # Extension root
│   ├── src/                     # Source code
│   │   ├── background/          # Background service worker
│   │   │   ├── index.ts         # Main background script
│   │   │   └── tabManager.ts    # Tab management logic
│   │   ├── shared/              # Shared code
│   │   │   ├── types.ts         # TypeScript type definitions
│   │   │   └── utils.ts         # Utility functions
│   │   └── ui/                  # User interface
│   │       └── index.ts         # Side panel UI logic
│   ├── public/                  # Public assets
│   │   ├── manifest.json        # Extension manifest
│   │   ├── sidepanel.html       # Side panel HTML
│   │   ├── styles.css           # Styles
│   │   └── icons/               # Extension icons
│   ├── scripts/                 # Build scripts
│   ├── package.json             # NPM package configuration
│   ├── tsconfig.json            # TypeScript configuration
│   └── vite.config.ts           # Vite build configuration
└── build-nucnuc.bat             # Build helper script
```

## Development

### Prerequisites

- Node.js (v16 or newer)
- npm
- Chrome browser

### Installation

1. Clone this repository
2. Install dependencies:
```bash
cd nucnuc_new/extension
npm install
```

### Development Commands

#### Using the build script:

```bash
# Build the extension
.\build-nucnuc.bat build

# Run in development mode
.\build-nucnuc.bat dev

# Create a zip package
.\build-nucnuc.bat zip

# Fix service worker issues
.\build-nucnuc.bat fix-sw
```

#### Direct npm commands:

```bash
cd extension

# Build the extension
npm run build

# Run in development mode
npm run dev

# Create a zip package
npm run zip
```

### Loading in Chrome

1. Build the extension using one of the commands above
2. Open Chrome and navigate to `chrome://extensions/`
3. Enable "Developer mode" in the top right
4. Click "Load unpacked" and select the `extension/dist` directory
5. The extension will now appear in your browser toolbar

## Usage

### Tab Management

1. Click the NucNuc icon in the Chrome toolbar to open the side panel
2. Use the buttons to:
   - Group tabs by domain
   - Ungroup all tabs
   - Remove duplicate tabs
3. Configure settings:
   - Enable/disable automatic tab grouping
   - Set minimum tabs per group
   - Choose color scheme for tab groups

### Themes

Toggle between light and dark themes using the button in the top-right corner of the side panel.

## License

Private
