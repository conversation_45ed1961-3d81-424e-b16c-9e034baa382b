:root {
  --primary-color: #4a69bd;
  --primary-hover: #3c56a8;
  --bg-color: #ffffff;
  --bg-secondary: #f7f7f7;
  --text-color: #333333;
  --text-secondary: #666666;
  --border-color: #dddddd;
  --success-color: #2ecc71;
  --warning-color: #f39c12;
  --error-color: #e74c3c;
  --card-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  --transition-speed: 0.2s;
}

.theme-dark {
  --primary-color: #546de5;
  --primary-hover: #6979f8;
  --bg-color: #292929;
  --bg-secondary: #3a3a3a;
  --text-color: #e1e1e1;
  --text-secondary: #aaaaaa;
  --border-color: #555555;
  --card-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, <PERSON>linkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  font-size: 14px;
  color: var(--text-color);
  background-color: var(--bg-color);
  width: 100%;
  min-width: 300px;
  transition: background-color var(--transition-speed) ease;
}

#app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
}

header h1 {
  font-size: 18px;
  font-weight: 600;
}

main {
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

footer {
  padding: 12px 16px;
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

.card {
  background-color: var(--bg-secondary);
  border-radius: 8px;
  padding: 16px;
  box-shadow: var(--card-shadow);
}

.card h2 {
  font-size: 16px;
  margin-bottom: 12px;
  color: var(--text-color);
}

.btn-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.btn {
  background-color: var(--bg-color);
  color: var(--text-color);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  cursor: pointer;
  transition: all var(--transition-speed) ease;
}

.btn:hover {
  background-color: var(--bg-secondary);
}

.btn.primary {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.btn.primary:hover {
  background-color: var(--primary-hover);
}

.settings-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.setting {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toggle {
  appearance: none;
  position: relative;
  width: 40px;
  height: 20px;
  border-radius: 10px;
  background-color: var(--border-color);
  cursor: pointer;
  transition: background-color var(--transition-speed) ease;
}

.toggle:checked {
  background-color: var(--primary-color);
}

.toggle::before {
  content: "";
  position: absolute;
  top: 2px;
  left: 2px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: white;
  transition: transform var(--transition-speed) ease;
}

.toggle:checked::before {
  transform: translateX(20px);
}

input[type="number"], select {
  width: 80px;
  padding: 4px 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--bg-color);
  color: var(--text-color);
}

.theme-toggle {
  display: flex;
  align-items: center;
}

.theme-icon {
  width: 20px;
  height: 20px;
  fill: var(--text-color);
}

.theme-light .theme-icon-light,
.theme-dark .theme-icon-dark {
  display: none;
}

.status {
  font-size: 13px;
  color: var(--text-secondary);
}

.status.success {
  color: var(--success-color);
}

.status.warning {
  color: var(--warning-color);
}

.status.error {
  color: var(--error-color);
}

@media (min-width: 500px) {
  .btn-group {
    flex-direction: row;
    flex-wrap: wrap;
  }
}
