<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NucNuc</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div id="app" class="theme-light">
    <header>
      <h1>NucNuc</h1>
      <div class="theme-toggle">
        <button id="theme-toggle-btn" aria-label="Toggle theme">
          <svg class="theme-icon theme-icon-dark" viewBox="0 0 24 24" width="24" height="24">
            <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10zm0-2a8 8 0 1 1 0-16 8 8 0 0 1 0 16z"></path>
          </svg>
          <svg class="theme-icon theme-icon-light" viewBox="0 0 24 24" width="24" height="24">
            <path d="M12 18a6 6 0 1 1 0-12 6 6 0 0 1 0 12zm0-2a4 4 0 1 0 0-8 4 4 0 0 0 0 8zM11 1h2v3h-2V1zm0 19h2v3h-2v-3zM3.515 4.929l1.414-1.414L7.05 5.636 5.636 7.05 3.515 4.93zM16.95 18.364l1.414-1.414 2.121 2.121-1.414 1.414-2.121-2.121zm2.121-14.85l1.414 1.415-2.121 2.121-1.414-1.414 2.121-2.121zM5.636 16.95l1.414 1.414-2.121 2.121-1.414-1.414 2.121-2.121zM23 11v2h-3v-2h3zM4 11v2H1v-2h3z"></path>
          </svg>
        </button>
      </div>
    </header>

    <main>
      <section class="card">
        <h2>Tab Management</h2>
        <div class="btn-group">
          <button id="group-tabs-btn" class="btn primary">Group Tabs</button>
          <button id="ungroup-tabs-btn" class="btn">Ungroup All</button>
          <button id="remove-duplicates-btn" class="btn">Remove Duplicates</button>
        </div>
      </section>

      <section class="card">
        <h2>Settings</h2>
        <div class="settings-group">
          <div class="setting">
            <label for="auto-group-tabs">Auto Group Tabs</label>
            <input type="checkbox" id="auto-group-tabs" class="toggle">
          </div>
          <div class="setting">
            <label for="cleanup-duplicates">Auto Cleanup Duplicates</label>
            <input type="checkbox" id="cleanup-duplicates" class="toggle">
          </div>
          <div class="setting">
            <label for="min-tabs-per-group">Min Tabs Per Group</label>
            <input type="number" id="min-tabs-per-group" min="1" max="10" value="2">
          </div>
          <div class="setting">
            <label for="color-scheme">Color Scheme</label>
            <select id="color-scheme">
              <option value="auto">Auto</option>
              <option value="pastel">Pastel</option>
              <option value="vivid">Vivid</option>
            </select>
          </div>
        </div>
      </section>
    </main>

    <footer>
      <div id="status" class="status"></div>
    </footer>
  </div>
  <script type="module" src="sidepanel.js"></script>
</body>
</html>
