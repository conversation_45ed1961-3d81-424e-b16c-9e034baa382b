import { extractDomain, formatDomain, getColorForDomain } from '../shared/utils';

/**
 * Tab Manager Module
 * Handles all tab-related operations including grouping, suspension, and cleanup
 */

/**
 * Set up tab manager event listeners
 */
export function setupTabManager() {
  // Listen for tab creation
  chrome.tabs.onCreated.addListener(handleNewTab);

  // Listen for tab updates
  chrome.tabs.onUpdated.addListener(handleTabUpdate);
}

/**
 * Handle newly created tabs
 * @param tab The newly created tab
 */
function handleNewTab(tab: chrome.tabs.Tab) {
  // Auto-group the tab if enabled in settings
  chrome.storage.local.get("settings", (result) => {
    const settings = result.settings || {};
    if (settings.autoGroupTabs) {
      assignTabToGroup(tab);
    }
  });
}

/**
 * Handle tab updates (URL changes, etc.)
 * @param tabId ID of the updated tab
 * @param changeInfo Information about the change
 * @param tab The updated tab
 */
function handleTabUpdate(
  tabId: number, 
  changeInfo: chrome.tabs.TabChangeInfo, 
  tab: chrome.tabs.Tab
) {
  // Only handle complete loads with URL changes
  if (changeInfo.status === "complete" && tab.url) {
    chrome.storage.local.get("settings", (result) => {
      const settings = result.settings || {};
      if (settings.autoGroupTabs) {
        assignTabToGroup(tab);
      }
    });
  }
}

/**
 * Assign a tab to the appropriate group based on domain
 * @param tab The tab to assign to a group
 */
async function assignTabToGroup(tab: chrome.tabs.Tab) {
  if (!tab.url || tab.url.startsWith("chrome://")) {
    // Don't group chrome internal pages
    return;
  }

  try {
    const domain = extractDomain(tab.url);
    if (!domain) return;

    // Skip if the tab is already in a group
    // @ts-ignore - TAB_GROUP_ID_NONE might not be in type definitions
    if (tab.groupId && tab.groupId !== chrome.tabGroups.TAB_GROUP_ID_NONE) {
      return;
    }

    // First check if the domain already has a group
    const groups = await chrome.tabGroups.query({});
    const tabs = await chrome.tabs.query({});

    // Get all grouped tabs
    const groupedTabs = tabs.filter(
      // @ts-ignore - TAB_GROUP_ID_NONE might not be in type definitions
      (t) => t.groupId !== chrome.tabGroups.TAB_GROUP_ID_NONE
    );

    // Find existing groups for this domain
    const domainGroups = new Set<number>();
    for (const groupedTab of groupedTabs) {
      if (groupedTab.url && extractDomain(groupedTab.url) === domain) {
        domainGroups.add(groupedTab.groupId!);
      }
    }

    if (domainGroups.size > 0) {
      // Use the first existing group for this domain
      const targetGroupId = [...domainGroups][0];
      await chrome.tabs.group({
        tabIds: [tab.id!],
        groupId: targetGroupId,
      });
    } else {
      // Create a new group for this domain
      const groupId = await chrome.tabs.group({ tabIds: [tab.id!] });
      
      // Set the group title and color
      await chrome.tabGroups.update(groupId, {
        title: formatDomain(domain),
        color: getColorForDomain(domain),
      });
    }
  } catch (error) {
    console.error("Error assigning tab to group:", error);
  }
}

/**
 * Organize all tabs based on domain
 */
export async function organizeTabs() {
  try {
    const tabs = await chrome.tabs.query({});
    
    // Group tabs by domain
    const domainGroups: { [domain: string]: number[] } = {};

    // Skip tabs that are already grouped
    const ungroupedTabs = tabs.filter(
      (tab) =>
        // @ts-ignore - TAB_GROUP_ID_NONE might not be in type definitions
        tab.groupId === chrome.tabGroups.TAB_GROUP_ID_NONE &&
        tab.url &&
        !tab.url.startsWith("chrome://")
    );

    for (const tab of ungroupedTabs) {
      const domain = extractDomain(tab.url!);
      if (domain) {
        if (!domainGroups[domain]) {
          domainGroups[domain] = [];
        }
        domainGroups[domain].push(tab.id!);
      }
    }

    // Get settings to check minimum tabs per group
    const settings = await new Promise<any>((resolve) => {
      chrome.storage.local.get("settings", (result) => {
        resolve(result.settings || { tabGrouping: { minTabsPerGroup: 2 } });
      });
    });

    const minTabsPerGroup = settings.tabGrouping?.minTabsPerGroup || 2;

    // Create groups for domains with enough tabs
    for (const [domain, tabIds] of Object.entries(domainGroups)) {
      if (tabIds.length >= minTabsPerGroup) {
        const groupId = await chrome.tabs.group({ tabIds });
        
        await chrome.tabGroups.update(groupId, {
          title: formatDomain(domain),
          color: getColorForDomain(domain),
        });
      }
    }
    
    return { success: true, groupCount: Object.keys(domainGroups).length };
  } catch (error) {
    console.error("Error organizing tabs:", error);
    return { success: false, error };
  }
}

/**
 * Find and remove duplicate tabs
 */
export async function removeDuplicateTabs() {
  try {
    const tabs = await chrome.tabs.query({});
    const urlMap = new Map<string, chrome.tabs.Tab>();
    const duplicates: number[] = [];

    // Find duplicates
    for (const tab of tabs) {
      if (tab.url && !tab.url.startsWith("chrome://")) {
        if (urlMap.has(tab.url)) {
          // Keep the tab with either the most recently active or most recently created
          const existingTab = urlMap.get(tab.url)!;
          
          // Use tab index as a simple heuristic (higher index = more recent)
          if (existingTab.index > tab.index) {
            duplicates.push(tab.id!);
          } else {
            duplicates.push(existingTab.id!);
            urlMap.set(tab.url, tab);
          }
        } else {
          urlMap.set(tab.url, tab);
        }
      }
    }

    // Remove duplicates
    if (duplicates.length > 0) {
      await chrome.tabs.remove(duplicates);
    }
    
    return { success: true, removed: duplicates.length };
  } catch (error) {
    console.error("Error removing duplicate tabs:", error);
    return { success: false, error };
  }
}

/**
 * Ungroup all tabs
 */
export async function ungroupTabs() {
  try {
    const tabs = await chrome.tabs.query({});
    
    // Get all grouped tabs
    const groupedTabs = tabs.filter(
      (tab) => 
        // @ts-ignore - TAB_GROUP_ID_NONE might not be in type definitions
        tab.groupId !== chrome.tabGroups.TAB_GROUP_ID_NONE
    );

    // Collect tab IDs
    const tabIds = groupedTabs.map(tab => tab.id!);

    // Only proceed if there are grouped tabs
    if (tabIds.length > 0) {
      await chrome.tabs.ungroup(tabIds);
    }
    
    return { success: true, ungrouped: tabIds.length };
  } catch (error) {
    console.error("Error ungrouping tabs:", error);
    return { success: false, error };
  }
}
