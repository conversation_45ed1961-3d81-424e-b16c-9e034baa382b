/**
 * Utility functions for the extension
 */

/**
 * Extract domain from URL
 * @param url The URL to extract domain from
 * @returns The domain or null if invalid URL
 */
export function extractDomain(url: string): string | null {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname;
  } catch (e) {
    console.error("Error extracting domain:", e);
    return null;
  }
}

/**
 * Format domain for display
 * @param domain The domain to format
 * @param maxLength Maximum length before truncation
 * @returns Formatted domain string
 */
export function formatDomain(domain: string, maxLength = 15): string {
  let formatted = domain.replace(/^www\./, "");
  if (formatted.length > maxLength) {
    formatted = formatted.substring(0, maxLength) + "...";
  }
  return formatted;
}

/**
 * Get consistent color for a domain
 * @param domain The domain to get color for
 * @returns A color from Chrome's tab group colors
 */
export function getColorForDomain(domain: string): chrome.tabGroups.ColorEnum {
  // Available colors in Chrome tab groups
  const colors: chrome.tabGroups.ColorEnum[] = [
    "blue",
    "red",
    "yellow",
    "green",
    "pink",
    "purple",
    "cyan",
    "orange",
    "grey"
  ];

  // Simple hash function for consistent domain colors
  let hash = 0;
  for (let i = 0; i < domain.length; i++) {
    hash = domain.charCodeAt(i) + ((hash << 5) - hash);
  }

  // Use hash to select a color
  const colorIndex = Math.abs(hash) % colors.length;
  return colors[colorIndex];
}

/**
 * Save settings to chrome.storage.local
 * @param settings Settings object to save
 * @returns Promise that resolves when settings are saved
 */
export function saveSettings(settings: any): Promise<void> {
  return new Promise((resolve, reject) => {
    chrome.storage.local.set({ settings }, () => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
      } else {
        resolve();
      }
    });
  });
}

/**
 * Load settings from chrome.storage.local
 * @returns Promise that resolves with the settings object
 */
export function loadSettings(): Promise<any> {
  return new Promise((resolve, reject) => {
    chrome.storage.local.get("settings", (result) => {
      if (chrome.runtime.lastError) {
        reject(chrome.runtime.lastError);
      } else {
        resolve(result.settings || {});
      }
    });
  });
}

/**
 * Get default settings
 * @returns Default settings object
 */
export function getDefaultSettings() {
  return {
    theme: "light",
    notifications: true,
    autoGroupTabs: true,
    cleanupDuplicates: true,
    tabGrouping: {
      minTabsPerGroup: 2,
      colorScheme: "auto"
    }
  };
}
