# Building with bolt.new: Project Structure Guide

## Platform Overview
- Browser-based full-stack development platform
- Generates code from natural language
- Best for MVPs and prototypes
- Supports major frameworks (React, Vue, Next.js)
- May struggle with complex UI/server actions
- Uses WebContainers technology

## Creating a 10-Prompt Structure

### First Prompt Format
- State the final vision
- Specify initial foundational step
- Keep it simple and clear
- No error handling yet

### Remaining Prompts Format (2-10)
Each prompt has two parts:
1. **Fix/Verify**: Address issues from previous step
2. **Build**: Add new visible feature/functionality

### Key Principles
- Each step must show visible progress
- Balance frontend and backend development
- Keep instructions flexible
- Build features incrementally
- Steps should be independent but connected
- DO NOT include any specific frameworks or programming languages

### Effective Prompt Writing

1. **Clarity and Specificity**:
   - Write clear, specific, and descriptive instructions to reduce ambiguity
   - Use structured organization and delimiters for better comprehension

2. **Task Decomposition**:
   - Break down complex tasks into smaller, manageable parts
   - Focus on one step at a time

3. **Examples and Context**:
   - Include examples (multishot prompting) to enhance output quality
   - Provide relevant context for better understanding, especially for complex tasks

4. **Role and Reasoning**:
   - Assign specific roles to guide tone and content
   - Structure prompts to encourage step-by-step reasoning
   - Use chain-of-thought prompting for complex tasks

5. **Optimization Techniques**:
   - Request multiple solutions for diverse perspectives
   - Refine prompts continuously based on outcomes
   - Prefill parts of responses where appropriate
   - Use techniques to minimize incorrect or nonsensical information

6. **Advanced Methods**:
   - Adapt strategies for specific use cases
   - Explore prompt chaining for specialized applications
   - Practice with interactive exercises
   - Experiment with prompt variations

### Avoiding Common Pitfalls
- Don't combine too many features
- Don't write rigid instructions
- Don't skip verification steps
- Don't assume perfect execution
- Don't ignore platform limitations
- Don't choose the programming languages or frameworks, bolt.new will do that itself

## Template```markdown
Prompt 1:
"[Project vision + concrete first step]"

Prompts 2-10:
"Fix: [Previous step issues]
Build: [New feature with clear outcome]"
```
