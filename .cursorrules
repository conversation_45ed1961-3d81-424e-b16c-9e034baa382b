# [0] Cursor AI Rule File
# =======================================================
Continually:
    - Meticulously purge redundant code to avert future complications—securely remove any consolidated, documented files in the process.
    - Always embody the natural, inherent clarity of your code without resorting to unnecessary verbosity.
    - Optimize developer ergonomics by ensuring intuitive navigation throughout the system.
    - Throughout any project, assess the projectstructure and Codebase to identify any shortcomings or potential improvements that ensure clarity, coherence, and impact.
    - Let the natural clarity of your code take center stage, avoiding any unnecessary verbosity-and write code that is effortlessly comprehensible by future developers.

Exponential Improvement Through Structured Clarity:
    - Each component and rule explicitly interacts with the others, collectively creating a robust, intuitive, and highly maintainable Chrome extension codebase tailored for optimal development with autonomous LLM coding agents.

# [1] General Settings
# =======================================================

## Git Workflow
developmentWorkflow:
    versionControl:
        system: Git
        repository: "Use local '.git' in root of project"
        # Unifying the template's and JSON's branching strategies:
        # The template suggests: "intuitive-branch-workflow"
        # The JSON suggests: "Feature branches -> Pull requests -> Merge to main"
        # Both are included as a recommended combined approach:
        branchingStrategy: "intuitive feature branches -> Pull requests -> Merge to main"
        ciWorkflow: "Optional. If introduced, run lint & minimal tests upon each push."
    incrementalDevelopment:
        - Develop in small, clearly defined increments
        - Ensure each increment is functional and stable before proceeding

## Cursor AI Preferences
cursorAIGenerationPreferences:
    verbosity: "concise"
    modularity: "high"
    abstractionLevel: "low to moderate"
    explicitness: "high"
    aiInteraction:
        - prefer generating complete, self-contained module implementations
        - avoid generating overly abstracted or indirect solutions

## Prompt Guidance (from JSON)
promptGuidance:
    responseDetail: "concise"
    avoidExcessiveImports: true
    focusOnCoreFeatures: true
    allowIncrementalImprovements: true

## Autonomous Integrations
autonomous_integrations:
    principles:
        - Clear modular structure facilitating effortless code generation and adaptation by LLM tools
        - Simple, self-describing functions and clear data-flow for easy parsing by AI
    guidance:
        - Use concise and explicit naming and comments for clarity
        - Ensure clear separation of concerns across components (background, popup, shared utils)

## Testing and Validation Rules
unit_testing_frameworks:
    enabled: true
preferred_frameworks:
    - Jest
test_generation:
    auto_generate_tests_for_functions: true
testingStrategy:
    unitTests: "Required for utility and background modules. (JSON note: Encourage using a lightweight test runner for critical modules.)"
    integrationTests: "Recommended for key workflows (tabs/bookmarks lifecycle). (JSON note: Manually test with real tabs/bookmarks, optional Puppeteer.)"
    performanceBenchmarking:
        - Ensure operations complete within <100ms user-perceived response times
        - Regular memory profiling for resource efficiency validation

## Workflow Optimization
cursor_navigation:
    predict_next_cursor_position: true
context_awareness:
    include_project_files:
        [
            "extension/manifest.json",
            "extension/src/background/background.js",
            "extension/src/popup/popup.html",
            "extension/src/popup/popup.js",
        ]
natural_language_commands:
    enabled: true
examples:
    - "Generate a function to save tabs as bookmarks"
    - "Refactor popup.js to improve readability"

### Design Principles
designPrinciples:
    # Merged definitions from both template and JSON
    simplicity: "Favor direct, uncluttered solutions for both UI and code architecture. Provide only essential functionality and keep user interactions intuitive."
    performance: "Optimize for speed and minimal resource usage (memory, CPU). Ensure quick response times (<100ms) for core features (tab grouping, bookmarking)."
    clarity: "Write code that is self-explanatory. Add concise comments only where logic is non-obvious. Maintain consistent, discoverable folder structure."
    llmIntegration: "Structure code so LLMs can easily parse, extend, and refine features. Provide well-defined modules or hooks for expansions or third-party AI integration."
    scalability: "Enable future enhancements (e.g., domain-based grouping, ephemeral session tracking) without large-scale refactoring."

# [2] Cursor Rules Optimized for Chrome Extension Development
# =======================================================

### Code Generation Rules
autocomplete:
    prefer_snippets:
        - chrome.tabs.query
        - chrome.bookmarks.create
        - chrome.storage.local.set
        - chrome.runtime.onInstalled.addListener
code_refactoring:
    optimize_loops: true # Suggest converting loops to map/filter/reduce when possible.
    remove_unused_code: true # Automatically detect and suggest removal of unused variables or functions.
multi_line_edits: true # Enable suggestions for multi-line edits.

### General Settings
language: "JavaScript"
frameworks:
    - "Chrome Extensions API"
    - "Manifest V3"

### Coding Standards
coding_style:
    # Unifying both the template and JSON codeStyle settings
    language: "JavaScript (ES6+). TypeScript is allowed if configured."
    namingConventions:
        variablesFunctions: "camelCase"
        classes: "PascalCase"
        constants: "UPPER_CASE"
        cssClasses: "kebab-case"
    indentation: 2
    max_line_length: 120 # Template suggested 120; JSON had 100; using 120 here
    prefer_const_let: true
    semicolons: true # Template wants semicolons; JSON defaulted to false
    trailingComma: "none" # From JSON
    quotes: "single" # From JSON
    asyncHandling: "Prefer async/await for Chrome API calls. Check chrome.runtime.lastError on callbacks."
error_handling:
    enforce_try_catch: true
comments:
    require_jsdoc: false
    policy: "Only comment on complex logic or intent. Avoid trivial documentation. Use JSDoc when clarifying function usage or parameters."
    example: "/* Explains purpose, not the obvious. */"

### Chrome Extension Settings
chromeExtensionConfig:
    manifestVersion: 3
    # Combining template + JSON permissions
    requiredPermissions:
        - tabs
        - tabGroups
        - bookmarks
        - storage
        - alarms
    # Detailed structure from the template, plus the JSON service worker reference
    structure:
        manifest: "extension directory"
        background:
            type: "service_worker"
            scripts:
                - "extension/src/background/background.js"
        popup:
            html: "extension/src/popup/popup.html"
            scripts:
                - "extension/src/popup/popup.js"
            css:
                - "extension/src/popup/popup.css"
        contentScripts: "minimal, only if absolutely required"
chromeExtension:
    # Additional details from the JSON "chromeExtension" object
    permissions:
        - "storage"
        - "tabs"
        - "tabGroups"
        - "alarms"
    background:
        serviceWorker: "extension/src/background/index.js"
        type: "module"
    action:
        defaultPopup: "extension/src/popup/popup.html"
        defaultIcon:
            "16": "extension/public/icons/icon16.png"
            "48": "extension/public/icons/icon48.png"
            "128": "extension/public/icons/icon128.png"
    optionsPage:
        html: "extension/src/options/options.html"
    csp: "script-src 'self'; object-src 'self';"

### Chrome Extension Dependencies
dependencies:
    external: "Minimal; leverage native Chrome APIs extensively"
    criteriaForInclusion:
        - "Provides clear and significant benefit (e.g., reduces code complexity dramatically)"
        - "Does not significantly impact performance or bundle size"

### Visual Design Principles
visualDesignPrinciples:
    ui: "Minimalist and distraction-free, specifically tailored to developer workflows"
    ux: "Explicit, predictable, intuitive; frictionless navigation"
    performance: "Optimized for responsiveness, minimal resource usage"
    security: "Strict adherence to Chrome MV3 security policies, minimal permissions"

### Projects' Filestructure
projectStructure:
    # From the template
    root:
        - extension
          - manifest.json
          - assets (icons, static files)
          - src
          - background
          - background.js (service worker entry)
          - modules
          - tabManager.js (intelligent grouping, suspension, cleanup)
          - bookmarkManager.js (rapid-access bookmark handling)
          - messageHandler.js (inter-component communication)
          - popup
          - popup.html
          - popup.js
          - popup.css
          - shared
          - utils.js (reusable utility functions)
          - constants.js (project-wide constants and configuration)
          - options (optional, for later feature expansion)
    # Incorporate JSON architecture's example (for reference)
    fileStructureExample:
        - "app_chrome_nucnuc/"
        - "├── extension/           // Extension root"
        - "│   ├── manifest.json"
        - "│   ├── src/"
        - "│   │   ├── background/ // Service Worker logic"
        - "│   │   ├── popup/      // UI"
        - "│   │   ├── content/    // Content scripts if needed"
        - "│   │   ├── options/    // Options page if needed"
        - "│   │   ├── shared/     // Reusable modules"
        - "│   │   └── ...         // Additional directories"
        - "│   ├── public/         // Static assets"
        - "├── .cursorrules"
        - "└── README.md           // Project documentation"
    architectureRules:
        - "Keep background logic strictly in extension/src/background; use ES modules to break large tasks into smaller modules (e.g., tabManager.js, bookmarkManager.js)."
        - "Popup-specific UI code resides in extension/src/popup; no direct background logic there. Use messaging if needed."
        - "Place cross-cutting utilities in extension/src/shared/ (e.g., storage helpers, type definitions)."

### Key Functionalities (Merged with functionalAreas from JSON)
keyFunctionalities:
    tabManagement:
        automation:
            - "Intelligent domain/context-based tab grouping"
            - "Suspension (discarding) of inactive tabs"
            - "Duplicate tab detection & cleanup"
            - "Optionally suspend inactive tabs for memory savings (chrome.tabs.discard)"
            - "Implement periodic cleanup with chrome.alarms or ephemeral session saves"
        interaction:
            - "Clear, minimal UI for quick tab control (popup)"
            - "One-click actions (e.g., save/restore tab sessions)"
    bookmarkHandling:
        automation:
            - "Rapid bookmark/tag creation (potentially context-aware)"
            - "Bookmark deduplication"
            - "Quickly create, update, or remove bookmarks; possibly implement tagging or fuzzy search"
        interaction:
            - "Intuitive search with fuzzy matching capability"
            - "Quick-access bookmarks UI designed for coding workflows"
            - "Optional visual thumbnails or quick previews"
            - "Keep UI clutter minimal; advanced features behind toggles"
    popup:
        - "Keep the popup interface minimal (no more than ~3 primary elements)."
        - "Use straightforward CSS or a lightweight framework if beneficial."
        - "Offer essential controls (e.g. 'Group Tabs', 'Bookmark All', 'Cleanup Duplicates'). Avoid bloat."
    messaging:
        - "Use chrome.runtime.sendMessage or chrome.runtime.onMessage for background–popup communication."
        - "Maintain JSON-structured messages with a clear 'type' and 'payload'."

# Additional Content from JSON

### LLM Integration
llmIntegration:
    modularCode: "Write discrete modules so LLMs can add or refine features with minimal ripple effects."
    clearHooks: "Surface extension points (like tabManager.js) with exported functions for expansion."
    documentation: "Keep high-level README or in-file docstrings explaining each major module's responsibilities."

### Performance & Testing (Extended)
performance:
    avoidExcessiveDependencies: true
    useAsyncPatterns: true
    profileLargeOperations: "If a feature loops over many tabs or bookmarks, ensure efficient code or chunking to maintain responsiveness."

# [3] Current Project
# =======================================================
project:
    name: "NucNuc Chrome Extension"
    description: "A minimalistic, high-performance Chrome extension (Manifest V3) for automated tab and bookmark management. Simple Chrome extension optimizing tab and bookmark management, through the mouse—without needing to use a keyboard. Located in the 'extension' directory."
