/**
 * Shared type definitions for the extension
 */

export interface Settings {
  /**
   * User interface theme
   */
  theme: 'light' | 'dark';
  
  /**
   * Enable or disable notifications
   */
  notifications: boolean;
  
  /**
   * Automatically group tabs by domain
   */
  autoGroupTabs: boolean;
  
  /**
   * Automatically clean up duplicate tabs
   */
  cleanupDuplicates: boolean;
  
  /**
   * Tab grouping options
   */
  tabGrouping: TabGroupingOptions;
}

export interface TabGroupingOptions {
  /**
   * Minimum number of tabs per group
   */
  minTabsPerGroup: number;
  
  /**
   * Color scheme for tab groups
   */
  colorScheme: 'auto' | 'pastel' | 'vivid';
}

/**
 * Status messages for UI feedback
 */
export type StatusType = 'info' | 'success' | 'warning' | 'error';

export interface StatusMessage {
  type: StatusType;
  text: string;
  duration?: number;
}
