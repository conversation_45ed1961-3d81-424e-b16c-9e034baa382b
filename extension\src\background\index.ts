/**
 * Background Service Worker for NucNuc Chrome Extension
 * Handles all background processes and API interactions.
 */

import { getDefaultSettings } from '../shared/utils';
import { Settings } from '../shared/types';
import { setupTabManager, organizeTabs, removeDuplicateTabs, ungroupTabs } from './tabManager';

// Listen for installation event
chrome.runtime.onInstalled.addListener((details) => {
  console.log("Extension installed or updated:", details.reason);

  // Initialize storage with default settings if needed
  if (details.reason === 'install') {
    const defaultSettings = getDefaultSettings();

    chrome.storage.local.set({ 
      settings: defaultSettings,
      lastUpdated: new Date().toISOString()
    });
  }

  // Setup tab management
  setupTabManager();
});

// Add action click listener to open side panel
chrome.action.onClicked.addListener((tab) => {
  if (chrome.sidePanel) {
    // Using the current window for the side panel
    chrome.windows.getCurrent((window) => {
      if (window.id) {
        chrome.sidePanel.open({ 
          windowId: window.id,
          tabId: tab.id 
        });
      }
    });
  }
});

// Listen for messages from UI
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log("Message received:", message.type);

  // Handle different message types
  switch (message.type) {
    case 'GET_SETTINGS': {
      chrome.storage.local.get(['settings'], (result) => {
        const settings = result.settings || getDefaultSettings();
        sendResponse({ success: true, data: settings });
      });
      return true; // Indicates asynchronous response
    }
    
    case 'UPDATE_SETTINGS': {
      chrome.storage.local.set({
        settings: message.data,
        lastUpdated: new Date().toISOString()
      }, () => {
        sendResponse({ success: true });
      });
      return true;
    }
    
    case 'ORGANIZE_TABS': {
      // Group tabs by domain
      organizeTabs().then(result => {
        sendResponse(result);
      });
      return true;
    }
    
    case 'UNGROUP_TABS': {
      // Ungroup all tabs
      ungroupTabs().then(result => {
        sendResponse(result);
      });
      return true;
    }
    
    case 'REMOVE_DUPLICATES': {
      // Remove duplicate tabs
      removeDuplicateTabs().then(result => {
        sendResponse(result);
      });
      return true;
    }
    
    default:
      sendResponse({ success: false, error: 'Unknown message type' });
      return false;
  }
});

// Log that service worker has started
console.log('NucNuc background service worker initialized');
