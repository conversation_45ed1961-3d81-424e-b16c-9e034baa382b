@echo off
setlocal enabledelayedexpansion

echo NucNuc Chrome Extension Build Script
echo ===================================
echo.

cd extension

if "%1"=="" (
  goto build
) else if "%1"=="build" (
  goto build
) else if "%1"=="dev" (
  goto dev
) else if "%1"=="zip" (
  goto zip
) else if "%1"=="fix-sw" (
  goto fix_sw
) else (
  echo Unknown command: %1
  echo Available commands: build, dev, zip, fix-sw
  goto end
)

:build
echo Building extension...
call npm run build
if %ERRORLEVEL% neq 0 (
  echo Build failed!
  goto end
)
echo.
echo Build completed successfully!
goto end

:dev
echo Starting development server...
call npm run dev
goto end

:zip
echo Creating ZIP package...
call npm run zip
if %ERRORLEVEL% neq 0 (
  echo ZIP creation failed!
  goto end
)
echo.
echo ZIP package created successfully!
goto end

:fix_sw
echo Fixing service worker...
node scripts/build-extension.js
echo.
echo Service worker fixed!
goto end

:end
cd ..
echo.
echo Done!
