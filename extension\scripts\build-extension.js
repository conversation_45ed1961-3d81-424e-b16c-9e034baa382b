import { cp, mkdir, readFile, writeFile } from 'fs/promises';
import { exec } from 'child_process';
import { join } from 'path';
import { promisify } from 'util';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const execAsync = promisify(exec);

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const ROOT_DIR = join(__dirname, '..');
const PUBLIC_DIR = join(ROOT_DIR, 'public');
const DIST_DIR = join(ROOT_DIR, 'dist');

/**
 * Build the extension
 */
async function buildExtension() {
  try {
    console.log('Building extension...');
    
    // Ensure dist directory exists
    await mkdir(DIST_DIR, { recursive: true });
    
    // Step 1: Build TypeScript with Vite
    console.log('Building TypeScript files...');
    await execAsync('npx vite build', { cwd: ROOT_DIR });
    
    // Step 2: Copy public files to dist
    console.log('Copying public files...');
    await copyPublicFiles();
    
    // Step 3: Fix any necessary files
    console.log('Performing post-processing...');
    await postProcess();
    
    console.log('Build completed successfully!');
  } catch (error) {
    console.error('Error building extension:', error);
    process.exit(1);
  }
}

/**
 * Copy files from public to dist
 */
async function copyPublicFiles() {
  try {
    // Copy manifest.json
    await cp(
      join(PUBLIC_DIR, 'manifest.json'),
      join(DIST_DIR, 'manifest.json')
    );
    
    // Copy sidepanel.html
    await cp(
      join(PUBLIC_DIR, 'sidepanel.html'),
      join(DIST_DIR, 'sidepanel.html')
    );
    
    // Copy styles.css
    await cp(
      join(PUBLIC_DIR, 'styles.css'),
      join(DIST_DIR, 'styles.css')
    );
    
    // Create icons directory and copy icons
    const iconsDir = join(DIST_DIR, 'icons');
    await mkdir(iconsDir, { recursive: true });
    
    // Note: Icons would need to be copied here
    // For now, we'll just log a message since we don't have actual icon files
    console.log('Note: Icon files need to be added to public/icons and copied to dist/icons');
  } catch (error) {
    console.error('Error copying public files:', error);
    throw error;
  }
}

/**
 * Perform any necessary post-processing on built files
 */
async function postProcess() {
  try {
    // Fix service worker for Chrome compatibility
    // Sometimes needed to ensure ES modules are used properly
    const backgroundPath = join(DIST_DIR, 'background.js');
    
    // Read the file
    let content = await readFile(backgroundPath, 'utf8');
    
    // Remove any CommonJS export that might cause issues
    content = content.replace(/exports\.\S+ = void 0;/g, '');
    content = content.replace(/Object\.defineProperty\(exports, ['"]__esModule['"], \{\s*value:\s*true\s*\}\);/g, '');
    
    // Write the modified content back
    await writeFile(backgroundPath, content, 'utf8');
  } catch (error) {
    console.error('Error in post-processing:', error);
    throw error;
  }
}

buildExtension();
